// Chrome DevTools 定时执行脚本（手动控制版本）
(function() {
    let intervalId;
    let taskHistory = [];
    let progressLayer = null;
    let cachedId = null; // 缓存上一次的 id 值
    
    // 创建任务进展浮层
    function createProgressLayer() {
        if (progressLayer) return;
        
        progressLayer = document.createElement('div');
        progressLayer.id = 'taskProgressLayer';
        progressLayer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 300px;
            max-height: 500px;
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 9999;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
            font-size: 14px;
            overflow: hidden;
        `;
        
        progressLayer.innerHTML = `
            <div style="
                padding: 12px 16px;
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                font-weight: 600;
                display: flex;
                justify-content: space-between;
                align-items: center;
            ">
                <span>任务执行控制台</span>
                <button id="closeProgressLayer" style="
                    background: none;
                    border: none;
                    color: white;
                    font-size: 18px;
                    cursor: pointer;
                    padding: 0;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                ">×</button>
            </div>
            <div id="progressContent" style="
                padding: 16px;
                max-height: 400px;
                overflow-y: auto;
            ">
                <div style="
                    margin-bottom: 16px;
                    display: flex;
                    gap: 8px;
                ">
                    <button id="startTimerBtn" style="
                        flex: 1;
                        padding: 8px 12px;
                        background: #48bb78;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        font-size: 12px;
                        font-weight: 500;
                        cursor: pointer;
                        transition: background 0.2s;
                    ">开始定时执行</button>
                    <button id="stopTimerBtn" style="
                        flex: 1;
                        padding: 8px 12px;
                        background: #e53e3e;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        font-size: 12px;
                        font-weight: 500;
                        cursor: pointer;
                        transition: background 0.2s;
                        opacity: 0.5;
                    " disabled>停止定时执行</button>
                </div>
                <div style="
                    margin-bottom: 16px;
                    display: flex;
                    gap: 8px;
                ">
                    <button id="executeNowBtn" style="
                        flex: 1;
                        padding: 8px 12px;
                        background: #667eea;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        font-size: 12px;
                        font-weight: 500;
                        cursor: pointer;
                        transition: background 0.2s;
                    ">立即执行一次</button>
                    <button id="clearHistoryBtn" style="
                        flex: 1;
                        padding: 8px 12px;
                        background: #ed8936;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        font-size: 12px;
                        font-weight: 500;
                        cursor: pointer;
                        transition: background 0.2s;
                    ">清空历史</button>
                </div>
                <div id="currentTaskStatus" style="
                    margin-bottom: 16px;
                    padding: 12px;
                    background: #f7fafc;
                    border-radius: 6px;
                    border-left: 4px solid #667eea;
                ">
                    <div style="font-weight: 600; color: #2d3748; margin-bottom: 4px;">当前状态</div>
                    <div id="currentStatus" style="color: #4a5568;">脚本已加载，等待操作...</div>
                </div>
                <div style="
                    font-weight: 600;
                    color: #2d3748;
                    margin-bottom: 12px;
                    padding-bottom: 8px;
                    border-bottom: 1px solid #e2e8f0;
                ">执行历史</div>
                <div id="taskHistoryList"></div>
            </div>
        `;
        
        document.body.appendChild(progressLayer);
        
        // 绑定按钮事件
        bindButtonEvents();
        
        // 让浮层可以拖拽移动
        makeLayerDraggable(progressLayer);
        
        // 初始化历史显示
        updateHistoryDisplay();
    }
    
    // 绑定按钮事件
    function bindButtonEvents() {
        // 关闭按钮
        document.getElementById('closeProgressLayer').addEventListener('click', function() {
            progressLayer.style.display = 'none';
        });
        
        // 开始定时执行按钮
        document.getElementById('startTimerBtn').addEventListener('click', function() {
            startTimer();
        });
        
        // 停止定时执行按钮
        document.getElementById('stopTimerBtn').addEventListener('click', function() {
            stopTimer();
        });
        
        // 立即执行一次按钮
        document.getElementById('executeNowBtn').addEventListener('click', function() {
            checkAndExecuteTask();
        });
        
        // 清空历史按钮
        document.getElementById('clearHistoryBtn').addEventListener('click', function() {
            taskHistory = [];
            updateHistoryDisplay();
            addTaskHistory('系统操作', '历史记录已清空');
        });
        
        // 按钮悬停效果
        const buttons = progressLayer.querySelectorAll('button[id$="Btn"]');
        buttons.forEach(btn => {
            btn.addEventListener('mouseenter', function() {
                if (!this.disabled) {
                    this.style.filter = 'brightness(1.1)';
                }
            });
            btn.addEventListener('mouseleave', function() {
                this.style.filter = 'brightness(1)';
            });
        });
    }
    
    // 更新按钮状态
    function updateButtonStates() {
        if (!progressLayer) return;
        
        const startBtn = document.getElementById('startTimerBtn');
        const stopBtn = document.getElementById('stopTimerBtn');
        
        if (intervalId) {
            startBtn.disabled = true;
            startBtn.style.opacity = '0.5';
            stopBtn.disabled = false;
            stopBtn.style.opacity = '1';
        } else {
            startBtn.disabled = false;
            startBtn.style.opacity = '1';
            stopBtn.disabled = true;
            stopBtn.style.opacity = '0.5';
        }
    }
    
    // 让浮层可拖拽
    function makeLayerDraggable(element) {
        let isDragging = false;
        let startX, startY, startLeft, startTop;
        
        const header = element.querySelector('div');
        
        header.addEventListener('mousedown', function(e) {
            if (e.target.id === 'closeProgressLayer') return;
            
            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;
            startLeft = parseInt(element.style.right) || 20;
            startTop = parseInt(element.style.top) || 20;
            
            header.style.cursor = 'grabbing';
        });
        
        document.addEventListener('mousemove', function(e) {
            if (!isDragging) return;
            
            const deltaX = startX - e.clientX;
            const deltaY = e.clientY - startY;
            
            element.style.right = (startLeft + deltaX) + 'px';
            element.style.top = (startTop + deltaY) + 'px';
        });
        
        document.addEventListener('mouseup', function() {
            isDragging = false;
            header.style.cursor = 'grab';
        });
        
        header.style.cursor = 'grab';
    }
    
    // 更新当前任务状态
    function updateCurrentStatus(status) {
        if (!progressLayer) createProgressLayer();
        
        const currentStatus = document.getElementById('currentStatus');
        if (currentStatus) {
            const now = new Date().toLocaleString('zh-CN');
            currentStatus.innerHTML = `${status} <span style="color: #718096; font-size: 12px;">(${now})</span>`;
        }
    }
    
    // 添加任务历史记录
    function addTaskHistory(taskName, status, duration = null) {
        const historyItem = {
            taskName,
            status,
            duration,
            timestamp: new Date().toLocaleString('zh-CN')
        };
        
        taskHistory.unshift(historyItem);
        
        // 只保留最近20条记录
        if (taskHistory.length > 20) {
            taskHistory = taskHistory.slice(0, 20);
        }
        
        updateHistoryDisplay();
    }
    
    // 更新历史记录显示
    function updateHistoryDisplay() {
        if (!progressLayer) return;
        
        const historyList = document.getElementById('taskHistoryList');
        if (!historyList) return;
        
        historyList.innerHTML = taskHistory.map(item => {
            const statusColor = item.status === '成功' ? '#48bb78' : 
                               item.status === '失败' ? '#e53e3e' : '#ed8936';
            const durationText = item.duration ? ` (${item.duration}ms)` : '';
            
            return `
                <div style="
                    margin-bottom: 8px;
                    padding: 8px;
                    background: #ffffff;
                    border: 1px solid #e2e8f0;
                    border-radius: 4px;
                    font-size: 12px;
                ">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-weight: 600; color: #2d3748;">${item.taskName}</span>
                        <span style="color: ${statusColor}; font-weight: 500;">${item.status}</span>
                    </div>
                    <div style="color: #718096; margin-top: 2px;">
                        ${item.timestamp}${durationText}
                    </div>
                </div>
            `;
        }).join('') || '<div style="color: #718096; text-align: center; padding: 20px;">暂无执行记录</div>';
    }
    
    // 获取接口数据并检查是否需要执行任务
    async function checkAndExecuteTask() {
        try {
            updateCurrentStatus('正在检查接口数据...');
            
            // 获取接口数据
            const response = await fetch('https://pre-code.alibaba-inc.com/api/v3/projects/cosy%2Fcosy-vscode-new/repository/branches/develop-ide?private_token=NtqtFLCJufYyEW9mK424');
            
            if (!response.ok) {
                throw new Error(`接口请求失败: ${response.status} ${response.statusText}`);
            }
            
            const data = await response.json();
            const currentId = data.id;
            
            if (currentId === undefined || currentId === null) {
                throw new Error('接口返回数据中没有找到 id 字段');
            }
            
            // 检查是否需要执行任务
            if (cachedId === null) {
                // 缓存为空，直接执行
                updateCurrentStatus(`首次执行，id: ${currentId}`);
                addTaskHistory('接口检查', '首次执行', null);
                cachedId = currentId;
                await executeTask();
            } else if (cachedId !== currentId) {
                // id 发生变化，执行任务
                updateCurrentStatus(`检测到 id 变化: ${cachedId} -> ${currentId}`);
                addTaskHistory('接口检查', `id 变化: ${cachedId} -> ${currentId}`, null);
                cachedId = currentId;
                await executeTask();
            } else {
                // id 相同，跳过执行
                updateCurrentStatus(`id 未变化: ${currentId}，跳过执行`);
                addTaskHistory('接口检查', `id 未变化: ${currentId}`, null);
            }
            
        } catch (error) {
            updateCurrentStatus(`接口检查失败: ${error.message}`);
            addTaskHistory('接口检查', '失败', null);
            console.error('接口检查错误:', error);
        }
    }
    
    // 执行任务的主函数
    async function executeTask() {
        try {
            updateCurrentStatus('开始执行任务周期...');
            const cycleStartTime = Date.now();
            
            // 执行第一个函数
            if (typeof generateQoderIDEScripts === 'function') {
                updateCurrentStatus('正在执行 generateQoderIDEScripts()...');
                const startTime = Date.now();
                
                try {
                    await generateQoderIDEScripts();
                    const duration = Date.now() - startTime;
                    addTaskHistory('generateQoderIDEScripts', '成功', duration);
                } catch (error) {
                    const duration = Date.now() - startTime;
                    addTaskHistory('generateQoderIDEScripts', '失败', duration);
                    throw error;
                }
            } else {
                addTaskHistory('generateQoderIDEScripts', '函数不存在');
            }
            
            // 等待5秒
            updateCurrentStatus('等待 5 秒...');
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // 执行第二个函数
            if (typeof runScripts === 'function') {
                updateCurrentStatus('正在执行 runScripts()...');
                const startTime = Date.now();
                
                try {
                    await runScripts();
                    const duration = Date.now() - startTime;
                    addTaskHistory('runScripts', '成功', duration);
                } catch (error) {
                    const duration = Date.now() - startTime;
                    addTaskHistory('runScripts', '失败', duration);
                    throw error;
                }
            } else {
                addTaskHistory('runScripts', '函数不存在');
            }
            
            const totalDuration = Date.now() - cycleStartTime;
            if (intervalId) {
                updateCurrentStatus(`任务周期完成，下次执行时间: ${new Date(Date.now() + 60000).toLocaleString('zh-CN')}`);
            } else {
                updateCurrentStatus('单次任务执行完成');
            }
            addTaskHistory('完整任务周期', '成功', totalDuration);
            
        } catch (error) {
            updateCurrentStatus('任务执行失败');
            addTaskHistory('任务周期', '失败');
        }
    }
    
    // 启动定时器
    function startTimer() {
        if (intervalId) return;
        
        updateCurrentStatus('定时器已启动，准备执行首次任务...');
        addTaskHistory('定时器控制', '已启动');
        
        // 立即执行一次
        checkAndExecuteTask();
        
        // 设置每60秒执行一次（60000毫秒 = 60秒）
        intervalId = setInterval(checkAndExecuteTask, 60000);
        
        updateButtonStates();
    }
    
    // 停止定时器
    function stopTimer() {
        if (intervalId) {
            clearInterval(intervalId);
            intervalId = null;
            updateCurrentStatus('定时器已停止');
            addTaskHistory('定时器控制', '已停止');
            updateButtonStates();
        }
    }
    
    // 显示/隐藏进展浮层
    function toggleProgressLayer() {
        if (!progressLayer) {
            createProgressLayer();
        } else {
            progressLayer.style.display = progressLayer.style.display === 'none' ? 'block' : 'none';
        }
    }
    
    window.QoderScriptTimer = {
        start: startTimer,
        stop: stopTimer,
        executeNow: checkAndExecuteTask,
        showProgress: toggleProgressLayer,
        getStatus: function() {
            return {
                isRunning: !!intervalId,
                intervalId: intervalId,
                nextExecution: intervalId ? new Date(Date.now() + 60000).toLocaleString() : null,
                historyCount: taskHistory.length,
                cachedId: cachedId
            };
        }
    };
    
    // 初始化浮层但不自动启动
    createProgressLayer();
})();