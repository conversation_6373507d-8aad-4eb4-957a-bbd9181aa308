<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪食蛇游戏 - Snake Game</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: white;
        }

        .game-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        h1 {
            margin-bottom: 20px;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .game-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .score, .high-score {
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 15px;
            border-radius: 20px;
            backdrop-filter: blur(5px);
        }

        canvas {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.7);
            display: block;
            margin: 0 auto 15px auto;
        }

        .controls {
            margin-top: 15px;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .controls p {
            margin: 5px 0;
        }

        .game-over {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            display: none;
            z-index: 1000;
            border: 2px solid #ff4444;
        }

        .game-over h2 {
            color: #ff4444;
            margin-bottom: 15px;
        }

        .restart-btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 1.1rem;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 15px;
        }

        .restart-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .start-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .start-content {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }

        .start-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.3rem;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .start-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        @media (max-width: 600px) {
            .game-container {
                margin: 10px;
                padding: 15px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            canvas {
                max-width: 90vw;
                height: auto;
            }
        }
    </style>
</head>
<body>
    <div class="start-screen" id="startScreen">
        <div class="start-content">
            <h1>🐍 贪食蛇游戏</h1>
            <p>使用方向键控制蛇的移动</p>
            <p>吃食物让蛇变长，避免撞到墙壁和自己</p>
            <button class="start-btn" onclick="startGame()">开始游戏</button>
        </div>
    </div>

    <div class="game-container">
        <h1>🐍 贪食蛇</h1>
        
        <div class="game-info">
            <div class="score">
                得分: <span id="score">0</span>
            </div>
            <div class="high-score">
                最高分: <span id="highScore">0</span>
            </div>
        </div>
        
        <canvas id="gameCanvas" width="400" height="400"></canvas>
        
        <div class="controls">
            <p>🎮 使用方向键移动</p>
            <p>🍎 吃红色食物增长蛇身</p>
            <p>⚠️ 避免撞墙和撞到自己</p>
            <p>🔄 按空格键重新开始</p>
        </div>
    </div>

    <div class="game-over" id="gameOver">
        <h2>🎮 游戏结束!</h2>
        <p>你的得分: <span id="finalScore">0</span></p>
        <button class="restart-btn" onclick="restartGame()">重新开始</button>
    </div>

    <script>
        // Canvas and context
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');

        // Game settings
        const gridSize = 20;
        const tileCount = canvas.width / gridSize;

        // Game state
        let snake = [{x: 10, y: 10}];
        let food = {};
        let dx = 0;
        let dy = 0;
        let score = 0;
        let highScore = localStorage.getItem('snakeHighScore') || 0;
        let gameRunning = false;
        let gameStarted = false;

        // Update high score display
        document.getElementById('highScore').textContent = highScore;

        // Generate random food position
        function generateFood() {
            food = {
                x: Math.floor(Math.random() * tileCount),
                y: Math.floor(Math.random() * tileCount)
            };
            
            // Make sure food doesn't spawn on snake
            for (let segment of snake) {
                if (segment.x === food.x && segment.y === food.y) {
                    generateFood();
                    return;
                }
            }
        }

        // Draw game elements
        function drawGame() {
            // Clear canvas
            ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Draw snake
            ctx.fillStyle = '#4CAF50';
            for (let segment of snake) {
                ctx.fillRect(segment.x * gridSize, segment.y * gridSize, gridSize - 2, gridSize - 2);
            }

            // Draw snake head with different color
            if (snake.length > 0) {
                ctx.fillStyle = '#66BB6A';
                ctx.fillRect(snake[0].x * gridSize, snake[0].y * gridSize, gridSize - 2, gridSize - 2);
            }

            // Draw food
            ctx.fillStyle = '#F44336';
            ctx.fillRect(food.x * gridSize, food.y * gridSize, gridSize - 2, gridSize - 2);
        }

        // Move snake
        function moveSnake() {
            const head = {x: snake[0].x + dx, y: snake[0].y + dy};
            snake.unshift(head);

            // Check if food eaten
            if (head.x === food.x && head.y === food.y) {
                score += 10;
                document.getElementById('score').textContent = score;
                generateFood();
            } else {
                snake.pop();
            }
        }

        // Check collisions
        function checkCollision() {
            const head = snake[0];

            // Wall collision
            if (head.x < 0 || head.x >= tileCount || head.y < 0 || head.y >= tileCount) {
                return true;
            }

            // Self collision
            for (let i = 1; i < snake.length; i++) {
                if (head.x === snake[i].x && head.y === snake[i].y) {
                    return true;
                }
            }

            return false;
        }

        // Game loop
        function gameLoop() {
            if (!gameRunning) return;

            moveSnake();

            if (checkCollision()) {
                gameOver();
                return;
            }

            drawGame();
        }

        // Start game
        function startGame() {
            document.getElementById('startScreen').style.display = 'none';
            gameStarted = true;
            gameRunning = true;
            
            // Reset game state
            snake = [{x: 10, y: 10}];
            dx = 0;
            dy = 0;
            score = 0;
            document.getElementById('score').textContent = score;
            
            generateFood();
            drawGame();
            
            // Start game loop
            setInterval(gameLoop, 150);
        }

        // Game over
        function gameOver() {
            gameRunning = false;
            
            // Update high score
            if (score > highScore) {
                highScore = score;
                localStorage.setItem('snakeHighScore', highScore);
                document.getElementById('highScore').textContent = highScore;
            }
            
            document.getElementById('finalScore').textContent = score;
            document.getElementById('gameOver').style.display = 'block';
        }

        // Restart game
        function restartGame() {
            document.getElementById('gameOver').style.display = 'none';
            startGame();
        }

        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            if (!gameStarted) return;
            
            const key = e.key;
            
            // Prevent reverse direction
            if (key === 'ArrowLeft' && dx === 0) {
                dx = -1;
                dy = 0;
            } else if (key === 'ArrowRight' && dx === 0) {
                dx = 1;
                dy = 0;
            } else if (key === 'ArrowUp' && dy === 0) {
                dx = 0;
                dy = -1;
            } else if (key === 'ArrowDown' && dy === 0) {
                dx = 0;
                dy = 1;
            } else if (key === ' ') {
                e.preventDefault();
                if (gameRunning) {
                    gameOver();
                } else {
                    restartGame();
                }
            }
        });

        // Initialize
        generateFood();
        drawGame();
    </script>
</body>
</html>