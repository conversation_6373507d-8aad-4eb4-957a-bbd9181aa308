# Empty To-Do List App & Tank Battle Game Repository

This repository contains two projects:
- A simple to-do list application
- A tank battle game developed using Phaser

## To-Do List Application

### Features
- Add, edit, and delete tasks
- Task properties: title, description, category, priority, due date
- Mark tasks as complete/incomplete
- Custom categories with color options
- Filter and sort tasks by status, priority, and category
- Keyword search
- Data persistence via localStorage
- Responsive design for mobile devices

### Technology Stack
- HTML5
- CSS3 (including CSS variables, responsive layouts)
- Vanilla JavaScript (ES6+)
- Font Awesome (CDN for icons)

### Installation and Usage
1. Clone or download the repository
2. Open `index.html` in a browser

### Technical Constraints
- Operates in environments with localStorage support
- No synchronization across devices/browsers
- Not suitable for multi-user use

## Tank Battle Game

### Features
- Player tank control and movement
- Shooting mechanics
- Enemy AI
- Multiple levels and maps
- Scoring system

### Technology Stack
- Phaser (for 2D game development)
- HTML5
- CSS3
- JavaScript

### Installation and Usage
1. Clone or download the repository
2. Open `game/index.html` in a browser

### Development Approach
The tank battle game was developed following these key steps:
1. Determining game requirements and features
2. Designing game interface and user interaction
3. Selecting Phaser as the development framework
4. Planning game levels and map design
5. Implementing core mechanics (movement, shooting, etc.)
6. Testing and fixing bugs

## Contributing
Contributions are welcome! Please feel free to submit issues or pull requests.

## License
This project is open source and available under the MIT License.