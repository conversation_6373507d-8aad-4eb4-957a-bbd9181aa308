class TodoApp {
    constructor() {
        this.appState = {
            tasks: [],
            categories: [],
            filter: {
                searchText: '',
                categoryId: 'all',
                showCompleted: 'all',
                priorityFilter: 'all'
            },

        this.init();
    }

    init() {
        this.loadData();

        if (this.appState.categories.length === 0) {
            this.appState.categories.push({
                id: this.generateId(),
                name: '默认',
                color: '#4a5568',
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }

        this.bindEvents();
        this.renderCategories();
        this.renderTasks();
    }

    bindEvents() {
        document.getElementById('addTaskBtn').addEventListener('click', () => {
            this.showTaskModal();
        });

        document.getElementById('addCategoryBtn').addEventListener('click', () => {
            this.showCategoryModal();
        });

        document.getElementById('searchBtn').addEventListener('click', () => {
            const searchText = document.getElementById('searchInput').value.trim();
            this.appState.filter.searchText = searchText;
            this.renderTasks();
        });

        document.getElementById('searchInput').addEventListener('keyup', (e) => {
            if (e.key === 'Enter') {
                const searchText = e.target.value.trim();
                this.appState.filter.searchText = searchText;
                this.renderTasks();
            }
        });

        document.getElementById('statusFilter').addEventListener('change', (e) => {
            this.appState.filter.showCompleted = e.target.value;
            this.renderTasks();
        });

        document.getElementById('priorityFilter').addEventListener('change', (e) => {
            this.appState.filter.priorityFilter = e.target.value;
            this.renderTasks();
        });

        document.getElementById('sortOption').addEventListener('change', (e) => {
            this.appState.sort.field = e.target.value;
            this.renderTasks();
        });

        document.getElementById('taskForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleTaskFormSubmit();
        });

        document.getElementById('categoryForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleCategoryFormSubmit();
        });

        document.querySelectorAll('.close-modal').forEach(element => {
            element.addEventListener('click', () => {
                document.getElementById('taskModal').style.display = 'none';
                document.getElementById('categoryModal').style.display = 'none';
            });
        });

        document.getElementById('categoryList').addEventListener('click', (e) => {
            if (e.target.classList.contains('category-item')) {
                const categoryId = e.target.dataset.id;
                this.appState.filter.categoryId = categoryId;
                
                document.querySelectorAll('.category-item').forEach(item => {
                    item.classList.remove('active');
                });
                e.target.classList.add('active');
                
                this.renderTasks();
            }
        });
    }

    showTaskModal(task = null) {
        const modal = document.getElementById('taskModal');
        const modalTitle = document.getElementById('modalTitle');
        const form = document.getElementById('taskForm');
        
        form.reset();
        
        const categorySelect = document.getElementById('taskCategory');
        categorySelect.innerHTML = '';
        
        this.appState.categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            categorySelect.appendChild(option);
        });
        
        if (task) {
            modalTitle.textContent = '编辑任务';
            document.getElementById('taskTitle').value = task.title;
            document.getElementById('taskDescription').value = task.description || '';
            document.getElementById('taskCategory').value = task.categoryId;
            document.getElementById('taskPriority').value = task.priority;
            
            if (task.dueDate) {
                const dueDate = new Date(task.dueDate);
                const year = dueDate.getFullYear();
                const month = String(dueDate.getMonth() + 1).padStart(2, '0');
                const day = String(dueDate.getDate()).padStart(2, '0');
                document.getElementById('taskDueDate').value = `${year}-${month}-${day}`;
            }
            
            form.dataset.taskId = task.id;
        } else {
            modalTitle.textContent = '添加任务';
            delete form.dataset.taskId;
        }
        
        modal.style.display = 'flex';
    }

    showCategoryModal(category = null) {
        const modal = document.getElementById('categoryModal');
        const modalTitle = document.getElementById('categoryModalTitle');
        const form = document.getElementById('categoryForm');
        
        form.reset();
        
        if (category) {
            modalTitle.textContent = '编辑类别';
            document.getElementById('categoryName').value = category.name;
            document.getElementById('categoryColor').value = category.color;
            
            form.dataset.categoryId = category.id;
        } else {
            modalTitle.textContent = '添加类别';
            delete form.dataset.categoryId;
        }
        
        modal.style.display = 'flex';
    }

    handleTaskFormSubmit() {
        const form = document.getElementById('taskForm');
        const taskId = form.dataset.taskId;
        
        const taskData = {
            title: document.getElementById('taskTitle').value.trim(),
            description: document.getElementById('taskDescription').value.trim(),
            categoryId: document.getElementById('taskCategory').value,
            priority: parseInt(document.getElementById('taskPriority').value),
            dueDate: document.getElementById('taskDueDate').value ? new Date(document.getElementById('taskDueDate').value) : null
        };
        
        if (!taskData.title) {
            this.showNotification('任务标题不能为空', 'error');
            return;
        }
        
        if (taskId) {
            this.updateTask(taskId, taskData);
        } else {
            this.createTask(taskData);
        }
        
        document.getElementById('taskModal').style.display = 'none';
    }

    handleCategoryFormSubmit() {
        const form = document.getElementById('categoryForm');
        const categoryId = form.dataset.categoryId;
        
        const categoryData = {
            name: document.getElementById('categoryName').value.trim(),
            color: document.getElementById('categoryColor').value
        };
        
        if (!categoryData.name) {
            this.showNotification('类别名称不能为空', 'error');
            return;
        }
        
        if (categoryId) {
            this.updateCategory(categoryId, categoryData);
        } else {
            this.createCategory(categoryData);
        }
        
        document.getElementById('categoryModal').style.display = 'none';
    }

    createTask(taskData) {
        const newTask = {
            id: this.generateId(),
            title: taskData.title,
            description: taskData.description,
            completed: false,
            categoryId: taskData.categoryId,
            priority: taskData.priority,
            dueDate: taskData.dueDate,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        this.appState.tasks.push(newTask);
        this.saveData();
        this.renderTasks();
        this.showNotification('任务创建成功', 'success');
    }

    updateTask(taskId, taskData) {
        const taskIndex = this.appState.tasks.findIndex(task => task.id === taskId);
        
        if (taskIndex !== -1) {
            const updatedTask = {
                ...this.appState.tasks[taskIndex],
                ...taskData,
                updatedAt: new Date()
            };
            
            this.appState.tasks[taskIndex] = updatedTask;
            this.saveData();
            this.renderTasks();
            this.showNotification('任务更新成功', 'success');
        }
    }

    deleteTask(taskId) {
        if (confirm('确定要删除这个任务吗？')) {
            this.appState.tasks = this.appState.tasks.filter(task => task.id !== taskId);
            this.saveData();
            this.renderTasks();
            this.showNotification('任务已删除', 'success');
        }
    }

    toggleTaskCompletion(taskId) {
        const taskIndex = this.appState.tasks.findIndex(task => task.id === taskId);
        
        if (taskIndex !== -1) {
            this.appState.tasks[taskIndex].completed = !this.appState.tasks[taskIndex].completed;
            this.appState.tasks[taskIndex].updatedAt = new Date();
            this.saveData();
            this.renderTasks();
        }
    }

    createCategory(categoryData) {
        const newCategory = {
            id: this.generateId(),
            name: categoryData.name,
            color: categoryData.color,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        this.appState.categories.push(newCategory);
        this.saveData();
        this.renderCategories();
        this.showNotification('类别创建成功', 'success');
    }

    updateCategory(categoryId, categoryData) {
        const categoryIndex = this.appState.categories.findIndex(category => category.id === categoryId);
        
        if (categoryIndex !== -1) {
            const updatedCategory = {
                ...this.appState.categories[categoryIndex],
                ...categoryData,
                updatedAt: new Date()
            };
            
            this.appState.categories[categoryIndex] = updatedCategory;
            this.saveData();
            this.renderCategories();
            this.showNotification('类别更新成功', 'success');
        }
    }

    deleteCategory(categoryId) {
        const tasksWithCategory = this.appState.tasks.filter(task => task.categoryId === categoryId);
        
        if (tasksWithCategory.length > 0) {
            if (confirm(`此类别下有 ${tasksWithCategory.length} 个任务，确定要删除吗？相关任务将移至默认类别。`)) {
                const defaultCategory = this.appState.categories.find(category => category.name === '默认');
                
                if (defaultCategory) {
                    this.appState.tasks.forEach(task => {
                        if (task.categoryId === categoryId) {
                            task.categoryId = defaultCategory.id;
                        }
                    });
                }
                
                this.appState.categories = this.appState.categories.filter(category => category.id !== categoryId);
                this.saveData();
                this.renderCategories();
                this.renderTasks();
                this.showNotification('类别已删除', 'success');
            }
        } else {
            if (confirm('确定要删除这个类别吗？')) {
                this.appState.categories = this.appState.categories.filter(category => category.id !== categoryId);
                this.saveData();
                this.renderCategories();
                this.showNotification('类别已删除', 'success');
            }
        }
    }

    renderCategories() {
        const categoryList = document.getElementById('categoryList');
        const taskCategorySelect = document.getElementById('taskCategory');
        
        categoryList.innerHTML = `<li class="category-item ${this.appState.filter.categoryId === 'all' ? 'active' : ''}" data-id="all">全部</li>`;
        
        this.appState.categories.forEach(category => {
            const categoryItem = document.createElement('li');
            categoryItem.className = `category-item ${this.appState.filter.categoryId === category.id ? 'active' : ''}`;
            categoryItem.dataset.id = category.id;
            
            categoryItem.innerHTML = `
                <span>
                    <span class="category-color" style="background-color: ${category.color}"></span>
                    ${category.name}
                </span>
                <div class="category-actions">
                    <button class="edit-category" data-id="${category.id}"><i class="fas fa-edit"></i></button>
                    <button class="delete-category" data-id="${category.id}"><i class="fas fa-trash"></i></button>
                </div>
            `;
            
            categoryList.appendChild(categoryItem);
        });
        
        document.querySelectorAll('.edit-category').forEach(button => {
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                const categoryId = e.target.closest('.edit-category').dataset.id;
                const category = this.appState.categories.find(cat => cat.id === categoryId);
                if (category) {
                    this.showCategoryModal(category);
                }
            });
        });
        
        document.querySelectorAll('.delete-category').forEach(button => {
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                const categoryId = e.target.closest('.delete-category').dataset.id;
                this.deleteCategory(categoryId);
            });
        });
    }

    renderTasks() {
        const taskList = document.getElementById('taskList');
        
        taskList.innerHTML = '';
        
        let filteredTasks = this.appState.tasks.filter(task => {
            const searchMatch = !this.appState.filter.searchText || 
                task.title.toLowerCase().includes(this.appState.filter.searchText.toLowerCase()) ||
                (task.description && task.description.toLowerCase().includes(this.appState.filter.searchText.toLowerCase()));
            
            const categoryMatch = this.appState.filter.categoryId === 'all' || task.categoryId === this.appState.filter.categoryId;
            
            const statusMatch = this.appState.filter.showCompleted === 'all' || 
                (this.appState.filter.showCompleted === 'completed' && task.completed) ||
                (this.appState.filter.showCompleted === 'active' && !task.completed);
            
            const priorityMatch = this.appState.filter.priorityFilter === 'all' || 
                task.priority.toString() === this.appState.filter.priorityFilter;
            
            return searchMatch && categoryMatch && statusMatch && priorityMatch;
        });
        
        filteredTasks.sort((a, b) => {
            const field = this.appState.sort.field;
            const direction = this.appState.sort.direction === 'asc' ? 1 : -1;
            
            if (field === 'dueDate') {
                if (!a.dueDate && !b.dueDate) return 0;
                if (!a.dueDate) return direction;
                if (!b.dueDate) return -direction;
                return (new Date(a.dueDate) - new Date(b.dueDate)) * direction;
            } else if (field === 'priority') {
                return (b.priority - a.priority) * direction;
            } else if (field === 'createdAt') {
                return (new Date(a.createdAt) - new Date(b.createdAt)) * direction;
            }
            
            return 0;
        });
        
        if (filteredTasks.length === 0) {
            taskList.innerHTML = '<div class="no-tasks">没有找到任务</div>';
            return;
        }
        
        filteredTasks.forEach(task => {
            const taskItem = document.createElement('div');
            taskItem.className = `task-item ${task.completed ? 'task-completed' : ''}`;
            
            const category = this.appState.categories.find(cat => cat.id === task.categoryId) || { name: '未分类', color: '#718096' };
            
            let dueDateDisplay = '';
            let dueDateClass = '';
            
            if (task.dueDate) {
                const dueDate = new Date(task.dueDate);
                const today = new Date();
                const tomorrow = new Date();
                tomorrow.setDate(today.getDate() + 1);
                
                const options = { year: 'numeric', month: 'short', day: 'numeric' };
                dueDateDisplay = dueDate.toLocaleDateString('zh-CN', options);
                
                if (dueDate < today) {
                    dueDateClass = 'due-date-overdue';
                } else if (dueDate < tomorrow) {
                    dueDateClass = 'due-date-soon';
                }
            }
            
            let priorityClass = '';
            let priorityText = '';
            
            switch (task.priority) {
                case 1:
                    priorityClass = 'priority-low';
                    priorityText = '低';
                    break;
            `;
            
            categoryList.appendChild(categoryItem);
        });
        
        // 添加类别编辑和删除事件
        document.querySelectorAll('.edit-category').forEach(button => {
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                const categoryId = e.target.closest('.edit-category').dataset.id;
                const category = this.appState.categories.find(cat => cat.id === categoryId);
                if (category) {
                    this.showCategoryModal(category);
                }
            });
        });
        
        document.querySelectorAll('.delete-category').forEach(button => {
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                const categoryId = e.target.closest('.delete-category').dataset.id;
                this.deleteCategory(categoryId);
            });
        });
    }

    /**
     * 渲染任务列表
     */
    renderTasks() {
        const taskList = document.getElementById('taskList');
        
        // 清空现有内容
        taskList.innerHTML = '';
        
        // 过滤和排序任务
        let filteredTasks = this.appState.tasks.filter(task => {
            // 搜索过滤
            const searchMatch = !this.appState.filter.searchText || 
                task.title.toLowerCase().includes(this.appState.filter.searchText.toLowerCase()) ||
                (task.description && task.description.toLowerCase().includes(this.appState.filter.searchText.toLowerCase()));
            
            // 类别过滤
            const categoryMatch = this.appState.filter.categoryId === 'all' || task.categoryId === this.appState.filter.categoryId;
            
            // 状态过滤
            const statusMatch = this.appState.filter.showCompleted === 'all' || 
                (this.appState.filter.showCompleted === 'completed' && task.completed) ||
                (this.appState.filter.showCompleted === 'active' && !task.completed);
            
            // 优先级过滤
            const priorityMatch = this.appState.filter.priorityFilter === 'all' || 
                task.priority.toString() === this.appState.filter.priorityFilter;
            
            return searchMatch && categoryMatch && statusMatch && priorityMatch;
        });
        
        // 排序任务
        filteredTasks.sort((a, b) => {
            const field = this.appState.sort.field;
            const direction = this.appState.sort.direction === 'asc' ? 1 : -1;
            
            if (field === 'dueDate') {
                // 处理可能为null的截止日期
                if (!a.dueDate && !b.dueDate) return 0;
                if (!a.dueDate) return direction;
                if (!b.dueDate) return -direction;
                return (new Date(a.dueDate) - new Date(b.dueDate)) * direction;
            } else if (field === 'priority') {
                return (b.priority - a.priority) * direction; // 高优先级在前
            } else if (field === 'createdAt') {
                return (new Date(a.createdAt) - new Date(b.createdAt)) * direction;
            }
            
            return 0;
        });
        
        // 如果没有任务，显示提示信息
        if (filteredTasks.length === 0) {
            taskList.innerHTML = '<div class="no-tasks">没有找到任务</div>';
            return;
        }
        
        // 渲染任务列表
        filteredTasks.forEach(task => {
            const taskItem = document.createElement('div');
            taskItem.className = `task-item ${task.completed ? 'task-completed' : ''}`;
            
            // 获取任务所属类别
            const category = this.appState.categories.find(cat => cat.id === task.categoryId) || { name: '未分类', color: '#718096' };
            
            // 格式化截止日期
            let dueDateDisplay = '';
            let dueDateClass = '';
            
            if (task.dueDate) {
                const dueDate = new Date(task.dueDate);
                const today = new Date();
                const tomorrow = new Date();
                tomorrow.setDate(today.getDate() + 1);
                
                // 格式化日期
                const options = { year: 'numeric', month: 'short', day: 'numeric' };
                dueDateDisplay = dueDate.toLocaleDateString('zh-CN', options);
                
                // 设置截止日期样式
                if (dueDate < today) {
                    dueDateClass = 'due-date-overdue';
                } else if (dueDate < tomorrow) {
                    dueDateClass = 'due-date-soon';
                }
            }
            
            // 设置优先级样式和文本
            let priorityClass = '';
            let priorityText = '';
            
            switch (task.priority) {
                case 1:
                    priorityClass = 'priority-low';
                    priorityText = '低';
                    break;
                case 2:
                    priorityClass = 'priority-medium';
                    priorityText = '中';
                    break;
                case 3:
                    priorityClass = 'priority-high';
                    priorityText = '高';
                    break;
            }
            
            taskItem.innerHTML = `
                <div class="task-content">
                    <div class="task-title">
                        <input type="checkbox" class="task-checkbox" data-id="${task.id}" ${task.completed ? 'checked' : ''}>
                        <span>${task.title}</span>
                    </div>
                    ${task.description ? `<div class="task-description">${task.description}</div>` : ''}
                    <div class="task-meta">
                        <span class="task-category" style="background-color: ${category.color}20; color: ${category.color}; border: 1px solid ${category.color}">
                            ${category.name}
                        </span>
                        <span class="task-priority">
                            <span class="priority-indicator ${priorityClass}"></span>
                            优先级: ${priorityText}
                        </span>
                        ${task.dueDate ? `
                            <span class="task-due-date ${dueDateClass}">
                                <i class="fas fa-calendar"></i>
                                ${dueDateDisplay}
                            </span>
                        ` : ''}
                    </div>
                </div>
                <div class="task-actions">
                    <button class="edit-task" data-id="${task.id}"><i class="fas fa-edit"></i></button>
                    <button class="delete-task" data-id="${task.id}"><i class="fas fa-trash"></i></button>
                </div>
            `;
            
            taskList.appendChild(taskItem);
        });
        
        // 添加任务复选框事件
        document.querySelectorAll('.task-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const taskId = e.target.dataset.id;
                this.toggleTaskCompletion(taskId);
            });
        });
        
        // 添加任务编辑和删除事件
        document.querySelectorAll('.edit-task').forEach(button => {
            button.addEventListener('click', (e) => {
                const taskId = e.target.closest('.edit-task').dataset.id;
                const task = this.appState.tasks.find(t => t.id === taskId);
                if (task) {
                    this.showTaskModal(task);
                }
            });
        });
        
        document.querySelectorAll('.delete-task').forEach(button => {
            button.addEventListener('click', (e) => {
                const taskId = e.target.closest('.delete-task').dataset.id;
                this.deleteTask(taskId);
            });
        });
    }

    /**
     * 显示通知
     * @param {string} message - 通知消息
     * @param {string} type - 通知类型 (success, error, warning)
     */
    showNotification(message, type = 'success') {
        const notification = document.getElementById('notification');
        notification.textContent = message;
        notification.className = `notification ${type} show`;
        
        // 3秒后隐藏通知
        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    }

    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
    }

    /**
     * 从本地存储加载数据
     */
    loadData() {
        try {
            const savedData = localStorage.getItem('todoAppData');
            
            if (savedData) {
                this.appState = JSON.parse(savedData);
                
                // 确保日期对象正确恢复
                this.appState.tasks.forEach(task => {
                    if (task.dueDate) {
                        task.dueDate = new Date(task.dueDate);
                    }
                    task.createdAt = new Date(task.createdAt);
                    task.updatedAt = new Date(task.updatedAt);
                });
                
                this.appState.categories.forEach(category => {
                    category.createdAt = new Date(category.createdAt);
                    category.updatedAt = new Date(category.updatedAt);
                });
            }
        } catch (error) {
            console.error('加载数据失败:', error);
            this.showNotification('加载数据失败，使用默认设置', 'error');
        }
    }

    /**
     * 保存数据到本地存储
     */
    saveData() {
        try {
            localStorage.setItem('todoAppData', JSON.stringify(this.appState));
        } catch (error) {
            console.error('保存数据失败:', error);
            this.showNotification('保存数据失败', 'error');
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new TodoApp();
});