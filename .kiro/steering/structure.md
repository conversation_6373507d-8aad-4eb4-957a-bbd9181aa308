# 项目结构与组织

## 文件结构

个人待办事项应用采用简单的文件结构，包含以下主要文件：

```
/
├── index.html      # 主HTML文件，包含应用的整体结构和UI组件
├── style.css       # CSS样式文件，定义应用的视觉外观和响应式布局
└── script.js       # JavaScript文件，包含应用的所有业务逻辑和交互功能
```

## 代码组织

### HTML 结构

HTML文件采用语义化标签组织，主要包含以下部分：

- `<header>`: 包含应用标题和搜索功能
- `<main>`: 主要内容区域，分为侧边栏和内容区
  - `<aside>`: 侧边栏，包含类别列表和过滤选项
  - `<section>`: 内容区，显示任务列表
- 模态框: 用于任务和类别的创建/编辑表单
- 通知组件: 显示操作反馈信息

### CSS 组织

CSS文件使用模块化方式组织，包含以下主要部分：

- 基础样式: 重置和全局变量定义
- 布局样式: 定义应用的整体布局结构
- 组件样式: 为各个UI组件定义样式
- 响应式样式: 使用媒体查询适应不同屏幕尺寸
- 动画和过渡: 定义UI交互动画效果

### JavaScript 组织

JavaScript代码采用面向对象方式组织，主要通过 `TodoApp` 类实现：

- 构造函数: 初始化应用状态和数据
- 事件绑定: 设置各种用户交互的事件处理
- 数据操作: 实现任务和类别的CRUD操作
- UI渲染: 动态生成和更新DOM元素
- 本地存储: 管理数据的保存和加载
- 辅助函数: 提供通用功能支持

## 数据流

应用采用单向数据流模式：

1. 用户交互触发事件处理函数
2. 事件处理函数更新应用状态
3. 状态更新触发UI重新渲染
4. 状态变化保存到本地存储

## 命名约定

- **HTML ID**: 使用驼峰命名法 (例如: `taskList`, `addCategoryBtn`)
- **CSS 类名**: 使用连字符分隔 (例如: `task-item`, `category-list`)
- **JavaScript 变量**: 使用驼峰命名法 (例如: `newTask`, `categoryId`)
- **JavaScript 方法**: 使用动词开头的驼峰命名法 (例如: `createTask`, `toggleTaskCompletion`)

## 代码风格

- 使用2或4空格缩进
- 使用分号结束JavaScript语句
- 优先使用ES6+特性 (箭头函数、模板字符串、解构赋值等)
- 使用有意义的变量和函数名称
- 添加适当的注释说明代码功能和逻辑