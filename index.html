<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人待办事项</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <header class="app-header">
            <h1>个人待办事项</h1>
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="搜索任务...">
                <button id="searchBtn"><i class="fas fa-search"></i></button>
            </div>
        </header>

        <main class="app-main">
                <div class="filters-container">
                    <h2>过滤</h2>
                    <div class="filter-group">
                        <label>状态</label>
                        <select id="statusFilter">
                            <option value="all">全部</option>
                            <option value="active">未完成</option>
                            <option value="completed">已完成</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>优先级</label>
                        <select id="priorityFilter">
                            <option value="all">全部</option>
                            <option value="1">低</option>
                            <option value="2">中</option>
                            <option value="3">高</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>排序</label>
                        <select id="sortOption">
                            <option value="dueDate">截止日期</option>
                            <option value="priority">优先级</option>
                            <option value="createdAt">创建时间</option>
                        </select>
                    </div>
                </div>
            </aside>

            <section class="content">
                <div class="tasks-header">
                    <h2>任务列表</h2>
                    <button id="addTaskBtn" class="btn btn-primary"><i class="fas fa-plus"></i> 添加任务</button>
                </div>
                
                <div id="taskList" class="task-list">
                    <!-- 任务将通过JavaScript动态添加 -->
                </div>
            </section>
        </main>

        <!-- 添加任务模态框 -->
        <div id="taskModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="modalTitle">添加任务</h2>
                    <span class="close-modal">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="taskForm">
                        <div class="form-group">
                            <label for="taskTitle">标题</label>
                            <input type="text" id="taskTitle" required>
                        </div>
                        <div class="form-group">
                            <label for="taskDescription">描述</label>
                            <textarea id="taskDescription"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="taskCategory">类别</label>
                            <select id="taskCategory">
                                <!-- 类别将通过JavaScript动态添加 -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="taskPriority">优先级</label>
                            <select id="taskPriority">
                                <option value="1">低</option>
                                <option value="2">中</option>
                                <option value="3">高</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="taskDueDate">截止日期</label>
                            <input type="date" id="taskDueDate">
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">保存</button>
                            <button type="button" class="btn btn-secondary close-modal">取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 添加类别模态框 -->
        <div id="categoryModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="categoryModalTitle">添加类别</h2>
                    <span class="close-modal">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="categoryForm">
                        <div class="form-group">
                            <label for="categoryName">名称</label>
                            <input type="text" id="categoryName" required>
                        </div>
                        <div class="form-group">
                            <label for="categoryColor">颜色</label>
                            <input type="color" id="categoryColor" value="#4a5568">
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">保存</button>
                            <button type="button" class="btn btn-secondary close-modal">取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 通知组件 -->
        <div id="notification" class="notification"></div>
    </div>
    
    <script src="script.js"></script>
    <script src="1.js"></script>
    <script src="2.js"></script>
</body>
</html>