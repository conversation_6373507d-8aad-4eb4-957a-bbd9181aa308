# 个人待办事项管理应用

## 项目简介
这是一个纯前端实现的个人待办事项（To-Do List）管理应用，无需后端，所有数据保存在浏览器本地。适合个人日常任务管理，也适合作为前端学习项目。

## 主要功能
- 新增、编辑、删除任务
- 任务可设置标题、描述、分类、优先级、截止日期
- 任务完成状态切换
- 支持自定义任务分类及颜色
- 任务筛选（状态、优先级、分类）与排序
- 关键词搜索
- 数据持久化（localStorage）
- 响应式设计，适配移动端

## 使用方法
1. 克隆或下载本仓库到本地
2. 用浏览器直接打开 `index.html` 文件即可使用

## 技术栈
- HTML5
- CSS3（含CSS变量、响应式布局）
- 原生 JavaScript（ES6+）
- [Font Awesome](https://fontawesome.com/) 图标库

## 目录结构
```
empty/
├── index.html      # 主页面结构
├── style.css       # 样式文件
├── script.js       # 应用核心逻辑
├── README.md       # 项目说明
└── ...             # 编辑器配置等
```

## 本地运行
无需安装任何依赖，直接用浏览器打开 `index.html` 即可。

---

如有建议或问题，欢迎反馈！ 