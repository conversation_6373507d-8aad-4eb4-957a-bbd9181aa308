<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tank Battle Game</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #2c2c2c;
            color: white;
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }
        
        .game-container {
            position: relative;
            border: 2px solid #4a4a4a;
            border-radius: 8px;
            background: #1a1a1a;
        }
        
        #gameCanvas {
            display: block;
            background: #333;
        }
        
        .game-ui {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 10;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            min-width: 200px;
        }
        
        .score {
            margin: 5px 0;
            font-size: 16px;
        }
        
        .health-bar {
            width: 100px;
            height: 10px;
            background: #666;
            border-radius: 5px;
            overflow: hidden;
            margin: 5px 0;
        }
        
        .health-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            transition: width 0.3s ease;
        }
        
        .controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            z-index: 10;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
        }
        
        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            z-index: 20;
            display: none;
        }
        
        .restart-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
        }
        
        .restart-btn:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div class="game-ui">
            <div class="score">Score: <span id="scoreValue">0</span></div>
            <div class="score">Level: <span id="levelValue">1</span></div>
            <div>Health:</div>
            <div class="health-bar">
                <div class="health-fill" id="healthFill" style="width: 100%"></div>
            </div>
            <div class="score">Enemies: <span id="enemyCount">0</span></div>
        </div>
        
        <div class="controls">
            <div>WASD - Move</div>
            <div>Mouse - Aim & Shoot</div>
            <div>R - Restart Game</div>
        </div>
        
        <div class="game-over" id="gameOver">
            <h2 id="gameOverTitle">Game Over</h2>
            <div id="finalScore">Final Score: 0</div>
            <button class="restart-btn" id="restartBtn">Restart Game</button>
        </div>
    </div>
    
    <!-- Game Scripts -->
    <script src="js/Vector2D.js"></script>
    <script src="js/GameObject.js"></script>
    <script src="js/Tank.js"></script>
    <script src="js/Bullet.js"></script>
    <script src="js/Enemy.js"></script>
    <script src="js/Level.js"></script>
    <script src="js/Game.js"></script>
    <script src="js/main.js"></script>
</body>
</html>