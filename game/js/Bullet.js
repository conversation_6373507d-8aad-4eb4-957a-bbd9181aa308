class Bullet extends GameObject {
    constructor(position, direction, isPlayerBullet = false) {
        super(position, new Vector2D(6, 6));
        this.direction = direction.normalize();
        this.speed = 300;
        this.velocity = this.direction.multiply(this.speed);
        this.isPlayerBullet = isPlayerBullet;
        this.damage = isPlayerBullet ? 25 : 15;
        this.lifetime = 3000; // 3秒后自动销毁
        this.createdAt = Date.now();
        
        // 子弹颜色
        this.color = isPlayerBullet ? '#FFD700' : '#FF6B6B';
    }

    update(deltaTime) {
        super.update(deltaTime);
        
        // 检查生命周期
        if (Date.now() - this.createdAt > this.lifetime) {
            this.destroy();
        }
    }

    draw(ctx) {
        ctx.save();
        ctx.translate(this.position.x, this.position.y);
        ctx.rotate(this.direction.angle());

        // 绘制子弹
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, this.size.x / 2, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制子弹边框
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 1;
        ctx.stroke();

        // 绘制子弹尾迹
        ctx.strokeStyle = this.color;
        ctx.lineWidth = 3;
        ctx.globalAlpha = 0.6;
        ctx.beginPath();
        ctx.moveTo(0, 0);
        ctx.lineTo(-10, 0);
        ctx.stroke();

        ctx.restore();
    }

    onHit(target) {
        if (target instanceof Tank) {
            target.takeDamage(this.damage);
        }
        this.destroy();
    }
} 