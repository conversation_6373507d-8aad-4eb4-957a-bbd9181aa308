class GameObject {
    constructor(position, size) {
        this.position = position || new Vector2D();
        this.size = size || new Vector2D(32, 32);
        this.velocity = new Vector2D();
        this.rotation = 0;
        this.active = true;
        this.id = Math.random().toString(36).substr(2, 9);
    }

    update(deltaTime) {
        this.position = this.position.add(this.velocity.multiply(deltaTime));
    }

    draw(ctx) {
        // 基类不绘制任何内容，由子类实现
    }

    getBounds() {
        return {
            left: this.position.x - this.size.x / 2,
            right: this.position.x + this.size.x / 2,
            top: this.position.y - this.size.y / 2,
            bottom: this.position.y + this.size.y / 2
        };
    }

    collidesWith(other) {
        const bounds1 = this.getBounds();
        const bounds2 = other.getBounds();

        return bounds1.left < bounds2.right &&
               bounds1.right > bounds2.left &&
               bounds1.top < bounds2.bottom &&
               bounds1.bottom > bounds2.top;
    }

    isOnScreen(canvasWidth, canvasHeight) {
        const bounds = this.getBounds();
        return bounds.right > 0 && bounds.left < canvasWidth &&
               bounds.bottom > 0 && bounds.top < canvasHeight;
    }

    destroy() {
        this.active = false;
    }
} 