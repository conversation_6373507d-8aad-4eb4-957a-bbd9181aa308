class Tank extends GameObject {
    constructor(position, isPlayer = false) {
        super(position, new Vector2D(40, 40));
        this.isPlayer = isPlayer;
        this.health = isPlayer ? 100 : 50;
        this.maxHealth = this.health;
        this.speed = isPlayer ? 150 : 80;
        this.rotationSpeed = 3;
        this.lastShot = 0;
        this.shotCooldown = isPlayer ? 300 : 800; // 毫秒
        this.bullets = [];
        this.target = null;
        this.aiState = 'patrol'; // patrol, chase, attack
        this.patrolTimer = 0;
        this.patrolDirection = Vector2D.fromAngle(Math.random() * Math.PI * 2);
        
        // 坦克颜色
        this.color = isPlayer ? '#4CAF50' : '#f44336';
        this.turretColor = isPlayer ? '#2E7D32' : '#D32F2F';
    }

    update(deltaTime) {
        super.update(deltaTime);
        
        if (!this.isPlayer) {
            this.updateAI(deltaTime);
        }
        
        // 更新子弹
        this.bullets = this.bullets.filter(bullet => bullet.active);
        this.bullets.forEach(bullet => bullet.update(deltaTime));
    }

    draw(ctx) {
        ctx.save();
        ctx.translate(this.position.x, this.position.y);
        ctx.rotate(this.rotation);

        // 绘制坦克主体
        ctx.fillStyle = this.color;
        ctx.fillRect(-this.size.x / 2, -this.size.y / 2, this.size.x, this.size.y);
        
        // 绘制坦克边框
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 2;
        ctx.strokeRect(-this.size.x / 2, -this.size.y / 2, this.size.x, this.size.y);

        // 绘制炮塔
        ctx.fillStyle = this.turretColor;
        ctx.fillRect(-8, -20, 16, 20);
        
        // 绘制炮管
        ctx.fillStyle = '#666';
        ctx.fillRect(-2, -25, 4, 15);

        ctx.restore();

        // 绘制子弹
        this.bullets.forEach(bullet => bullet.draw(ctx));
        
        // 绘制血条
        this.drawHealthBar(ctx);
    }

    drawHealthBar(ctx) {
        const barWidth = 50;
        const barHeight = 6;
        const barX = this.position.x - barWidth / 2;
        const barY = this.position.y - this.size.y / 2 - 15;
        
        // 背景
        ctx.fillStyle = '#333';
        ctx.fillRect(barX, barY, barWidth, barHeight);
        
        // 血条
        const healthPercent = this.health / this.maxHealth;
        ctx.fillStyle = healthPercent > 0.5 ? '#4CAF50' : healthPercent > 0.25 ? '#FF9800' : '#f44336';
        ctx.fillRect(barX, barY, barWidth * healthPercent, barHeight);
        
        // 边框
        ctx.strokeStyle = '#fff';
        ctx.lineWidth = 1;
        ctx.strokeRect(barX, barY, barWidth, barHeight);
    }

    shoot() {
        const now = Date.now();
        if (now - this.lastShot < this.shotCooldown) return;

        const bulletDirection = Vector2D.fromAngle(this.rotation);
        const bulletPosition = this.position.add(bulletDirection.multiply(25));
        const bullet = new Bullet(bulletPosition, bulletDirection, this.isPlayer);
        
        this.bullets.push(bullet);
        this.lastShot = now;
    }

    takeDamage(damage) {
        this.health -= damage;
        if (this.health <= 0) {
            this.destroy();
        }
    }

    move(direction, deltaTime) {
        const moveVector = Vector2D.fromAngle(this.rotation).multiply(direction * this.speed * deltaTime);
        this.position = this.position.add(moveVector);
    }

    rotate(direction, deltaTime) {
        this.rotation += direction * this.rotationSpeed * deltaTime;
    }

    updateAI(deltaTime) {
        if (!this.target || !this.target.active) {
            this.aiState = 'patrol';
        }

        switch (this.aiState) {
            case 'patrol':
                this.patrol(deltaTime);
                break;
            case 'chase':
                this.chase(deltaTime);
                break;
            case 'attack':
                this.attack(deltaTime);
                break;
        }
    }

    patrol(deltaTime) {
        this.patrolTimer += deltaTime;
        if (this.patrolTimer > 2000) { // 2秒后改变方向
            this.patrolDirection = Vector2D.fromAngle(Math.random() * Math.PI * 2);
            this.patrolTimer = 0;
        }

        // 移动到巡逻方向
        const targetRotation = this.patrolDirection.angle();
        this.rotateTowards(targetRotation, deltaTime);
        this.move(1, deltaTime);
    }

    chase(deltaTime) {
        if (!this.target) return;

        const direction = this.target.position.subtract(this.position);
        const targetRotation = direction.angle();
        
        this.rotateTowards(targetRotation, deltaTime);
        this.move(1, deltaTime);

        // 如果距离足够近，切换到攻击状态
        if (direction.magnitude() < 100) {
            this.aiState = 'attack';
        }
    }

    attack(deltaTime) {
        if (!this.target) return;

        const direction = this.target.position.subtract(this.position);
        const targetRotation = direction.angle();
        
        this.rotateTowards(targetRotation, deltaTime);
        this.shoot();

        // 如果距离太远，切换回追击状态
        if (direction.magnitude() > 150) {
            this.aiState = 'chase';
        }
    }

    rotateTowards(targetRotation, deltaTime) {
        let angleDiff = targetRotation - this.rotation;
        
        // 处理角度环绕
        while (angleDiff > Math.PI) angleDiff -= Math.PI * 2;
        while (angleDiff < -Math.PI) angleDiff += Math.PI * 2;

        const rotationDirection = angleDiff > 0 ? 1 : -1;
        const rotationAmount = Math.min(Math.abs(angleDiff), this.rotationSpeed * deltaTime);
        
        this.rotation += rotationDirection * rotationAmount;
    }

    setTarget(target) {
        this.target = target;
        if (target) {
            this.aiState = 'chase';
        }
    }
} 