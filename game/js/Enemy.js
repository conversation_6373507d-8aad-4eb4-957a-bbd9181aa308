class Enemy extends Tank {
    constructor(position) {
        super(position, false);
        this.type = this.getRandomType();
        this.applyTypeProperties();
    }

    getRandomType() {
        const types = ['basic', 'fast', 'tank', 'sniper'];
        const weights = [0.4, 0.3, 0.2, 0.1]; // 权重分配
        
        const random = Math.random();
        let cumulativeWeight = 0;
        
        for (let i = 0; i < types.length; i++) {
            cumulativeWeight += weights[i];
            if (random <= cumulativeWeight) {
                return types[i];
            }
        }
        
        return 'basic';
    }

    applyTypeProperties() {
        switch (this.type) {
            case 'basic':
                this.color = '#f44336';
                this.turretColor = '#D32F2F';
                this.health = 50;
                this.maxHealth = 50;
                this.speed = 80;
                this.shotCooldown = 800;
                this.damage = 15;
                break;
                
            case 'fast':
                this.color = '#FF9800';
                this.turretColor = '#F57C00';
                this.health = 30;
                this.maxHealth = 30;
                this.speed = 150;
                this.shotCooldown = 600;
                this.damage = 10;
                break;
                
            case 'tank':
                this.color = '#9C27B0';
                this.turretColor = '#7B1FA2';
                this.health = 100;
                this.maxHealth = 100;
                this.speed = 50;
                this.shotCooldown = 1000;
                this.damage = 25;
                break;
                
            case 'sniper':
                this.color = '#607D8B';
                this.turretColor = '#455A64';
                this.health = 40;
                this.maxHealth = 40;
                this.speed = 60;
                this.shotCooldown = 1500;
                this.damage = 40;
                break;
        }
    }

    draw(ctx) {
        super.draw(ctx);
        
        // 绘制敌人类型标识
        ctx.save();
        ctx.fillStyle = '#fff';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(this.type.toUpperCase(), this.position.x, this.position.y + this.size.y / 2 + 20);
        ctx.restore();
    }

    updateAI(deltaTime) {
        super.updateAI(deltaTime);
        
        // 特殊AI行为
        switch (this.type) {
            case 'fast':
                this.fastAI(deltaTime);
                break;
            case 'sniper':
                this.sniperAI(deltaTime);
                break;
        }
    }

    fastAI(deltaTime) {
        // 快速敌人会尝试保持距离
        if (this.target && this.aiState === 'attack') {
            const distance = this.target.position.distance(this.position);
            if (distance < 80) {
                // 后退
                const direction = this.position.subtract(this.target.position).normalize();
                this.position = this.position.add(direction.multiply(this.speed * deltaTime));
            }
        }
    }

    sniperAI(deltaTime) {
        // 狙击手会保持距离并精确射击
        if (this.target && this.aiState === 'attack') {
            const distance = this.target.position.distance(this.position);
            if (distance < 120) {
                // 保持距离
                const direction = this.position.subtract(this.target.position).normalize();
                this.position = this.position.add(direction.multiply(this.speed * deltaTime * 0.5));
            }
        }
    }
} 